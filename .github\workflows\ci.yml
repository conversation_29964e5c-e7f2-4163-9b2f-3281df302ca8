name: CI

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/ci.yml'
      - 'manga_translator/**'
      - 'test/**'
      - 'pyproject.toml'
      - 'requirements.txt'
      - 'requirements-dev.txt'
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/ci.yml'
      - 'manga_translator/**'
      - 'test/**'
      - 'pyproject.toml'
      - 'requirements.txt'
      - 'requirements-dev.txt'
jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]
      fail-fast: false
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python3 -mvenv venv
        venv/bin/pip install -r requirements.txt -r requirements-dev.txt
    - name: Test
      run: |
        venv/bin/pytest test
      timeout-minutes: 5
    - name: Lint *.py files
      run: |
        venv/bin/pylint $(git ls-files '*.py')
      if: always()
      continue-on-error: true # to not fail CI on lint errors