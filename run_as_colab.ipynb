{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# For users who don't have a GPU!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up the environment.\n", "!git clone https://github.com/zyddnys/manga-image-translator\n", "%cd manga-image-translator/\n", "!python -m pip install -r requirements.txt\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run with colab. GPU must be selected at runtime!\n", "from google.colab.output import eval_js\n", "url = eval_js(\"google.colab.kernel.proxyPort(5003)\")\n", "print('Open the link to use manga-image-translator! -> ', url)\n", "!python -m manga_translator --verbose --mode web --use-gpu"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}