# This is a list of the current keys, used by translation services.
# If you wish to use one of these services, you must provide a value for these keys.
# Non-empty values represent the default values for this field.
# 
# There are multiple ways to set environmental variables. 
# 
# One option:
#   Create a file in the project folder named: `.env`
#       Mac / Linux: manga-image-translator/.env
#           Windows: manga-image-translator\.env
#   
#   Place the values you wish to use within the file `.env`.
#   e.g.
#   EXAMPLE_API_KEY='abcdef123456'
# 
# Last updated: 2025-04-28



# baidu
BAIDU_APP_ID = '' #你的appid
BAIDU_SECRET_KEY = '' #你的密钥

# youdao
YOUDAO_APP_KEY = '' # 应用ID
YOUDAO_SECRET_KEY = '' # 应用秘钥

# deepl
#   Free tier available: https://www.deepl.com/en/pro#developer
#   API Key: https://www.deepl.com/en/your-account/keys
DEEPL_AUTH_KEY = '' #YOUR_AUTH_KEY

# openai/ChatGPT
OPENAI_API_KEY = ''
OPENAI_API_BASE = 'https://api.openai.com/v1' #使用api-for-open-llm例子 http://127.0.0.1:8000/v1
OPENAI_MODEL = ''
OPENAI_HTTP_PROXY = '' # TODO: Replace with --proxy

# glossary
OPENAI_GLOSSARY_PATH = './dict/mit_glossary.txt'

# Groq (Not to be confused with Twitter's 'Grok')
#   Free Tier available: https://console.groq.com/docs/rate-limits
#   API Key: https://console.groq.com/keys
#   Available Models: https://console.groq.com/docs/models
GROQ_API_KEY = ''
GROQ_MODEL='mixtral-8x7b-32768'

# sakura
SAKURA_API_BASE = 'http://127.0.0.1:8080/v1' #SAKURA API地址
SAKURA_VERSION = '0.9' #SAKURA API版本，可选值：0.9、0.10，选择0.10则会加载术语表。
SAKURA_DICT_PATH = './sakura_dict.txt' #SAKURA 术语表路径

# Caiyun
CAIYUN_TOKEN = '' # 彩云小译API访问令牌

# Gemini
#   Free to use, usage is "Used to improve our products"
#       https://ai.google.dev/gemini-api/docs/pricing
# 
#   Get API key: https://aistudio.google.com/app/apikey
#   List of available models: https://ai.google.dev/gemini-api/docs/models/gemini
GEMINI_API_KEY = '' 
GEMINI_MODEL = 'gemini-1.5-flash-002' 


# DeepSeek
#   Get API key: https://platform.deepseek.com/api_keys
#   Pricing: https://api-docs.deepseek.com/quick_start/pricing
#       - Discount available during off-peak hours (16:30-00:30 UTC each day)
#   Models:
#       - deepseek-chat
#       - deepseek-reasoner
DEEPSEEK_API_KEY = ''
DEEPSEEK_API_BASE  = 'https://api.deepseek.com'
DEEPSEEK_MODEL  = 'deepseek-chat'

# ollama, (with OpenAI API compatibility)
#   CUSTOM_OPENAI_MODEL_CONF
#       Used for parsing the `gpt_config` file, grouping configurations together
#   For Ollama:
#       To change IP / Port: 
#           Set `OLLAMA_HOST` variable and relaunch Ollama service:
#           https://github.com/ollama/ollama/blob/main/docs/faq.md#how-do-i-configure-ollama-server
#       
#       Set `CUSTOM_OPENAI_MODEL` to the exact model you wish to use (e.g. 'qwen2.5:7b')
#               Be sure to pull and run the model before use
CUSTOM_OPENAI_API_KEY = 'ollama' # Unused for ollama, but maybe useful for other LLM tools.
CUSTOM_OPENAI_API_BASE = 'http://localhost:11434/v1' 
CUSTOM_OPENAI_MODEL = '' 
CUSTOM_OPENAI_MODEL_CONF = ''
