services:
  manga_image_translator:
    build:
      context: ./../../
    container_name: manga_image_translator_localdev
    # Batch Mode
    command: local --verbose --config-file --image "/app/Manga" # (FOR GPU) --use-gpu  
    # Web Mode 
    #entrypoint: python
    #command: server/main.py --start-instance --host=0.0.0.0 --port=5003 # (FOR GPU) --use-gpu

    volumes:
      - ./../../manga_image_translator_localdev:/app
      
    ports:
      - 5003:5003

    ipc: host

    # For GPU
    #deploy:
    #  resources:
    #    reservations:
    #      devices:
    #        - capabilities: [gpu]
