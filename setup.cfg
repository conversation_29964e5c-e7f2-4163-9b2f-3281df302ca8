
[flake8]
exclude = build,venv,.tox,.git,.pytest_cache
ignore = E402,E501,E731,E741,W503
max_line_length = 120
per_file_ignores =
    devscripts/lazy_load_template.py: F401


[autoflake]
ignore-init-module-imports = true
ignore-pass-after-docstring = true
remove-all-unused-imports = true
remove-duplicate-keys = true
remove-unused-variables = true

[options]
packages = find:
package_dir =
    = .

[options.packages.find]
include =
    manga_translator
