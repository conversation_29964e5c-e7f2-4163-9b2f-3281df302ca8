networkx
torch
groq
torchvision
torch-summary
einops
scikit-image
opencv-python
pyclipper
shapely
requests
cryptography
freetype-py
aiohttp
tqdm
deepl
ImageHash
kornia
backports.cached-property
huggingface_hub
transformers
py3langid==0.2.2
sentencepiece
editdistance
numpy==1.26.4
tensorboardX
websockets
protobuf<6.0.0,>=3.20.2 # `google-genai` dependancy requirement
ctranslate2<=3.24.0 # last version compatible with CUDA 11
colorama
openai==1.63.0
tiktoken
httpx==0.27.2 # stop before blocking change in 0.28.0
open_clip_torch
safetensors
pandas
onnxruntime
timm
omegaconf
python-dotenv
nest-asyncio
marshmallow
cython
aioshutil
aiofiles
arabic-reshaper
pyhyphen
langcodes
manga-ocr
langdetect
pydensecrf@https://github.com/lucasb-eyer/pydensecrf/archive/refs/heads/master.zip
accelerate
bitsandbytes
uvicorn
fastapi
pydantic==2.5.0
python-multipart
google-genai
rich
regex

# Currently CUDA 11.8 and 12.3 are supported. 
# Let pip choose the cuda version to use:
--extra-index-url https://www.paddlepaddle.org.cn/packages/stable/cu118/
--extra-index-url https://www.paddlepaddle.org.cn/packages/stable/cu123/
--extra-index-url https://www.paddlepaddle.org.cn/packages/stable/cu126/
--extra-index-url https://www.paddlepaddle.org.cn/packages/stable/cu129/
paddleocr
paddlepaddle
paddlepaddle-gpu; sys_platform != 'darwin'
